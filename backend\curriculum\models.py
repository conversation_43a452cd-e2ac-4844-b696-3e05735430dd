from django.db import models
from django.contrib.auth import get_user_model
from courses.models import Course

User = get_user_model()

class CurriculumTemplate(models.Model):
    """培养方案模板"""
    school = models.CharField(max_length=100, verbose_name='学校')
    major = models.CharField(max_length=100, verbose_name='专业')
    enrollment_year = models.CharField(max_length=10, verbose_name='适用年份')
    total_credits_required = models.IntegerField(verbose_name='总学分要求')
    description = models.TextField(blank=True, verbose_name='方案描述')
    is_active = models.BooleanField(default=True, verbose_name='是否激活')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '培养方案模板'
        verbose_name_plural = '培养方案模板'
        db_table = 'curriculum_templates'
        unique_together = ['school', 'major', 'enrollment_year']

    def __str__(self):
        return f"{self.school} - {self.major} ({self.enrollment_year})"

class CreditRequirement(models.Model):
    """学分要求"""
    template = models.ForeignKey(CurriculumTemplate, on_delete=models.CASCADE, verbose_name='培养方案模板')
    category = models.CharField(max_length=50, verbose_name='课程类别')
    required_credits = models.IntegerField(verbose_name='要求学分')
    description = models.TextField(blank=True, verbose_name='要求描述')

    class Meta:
        verbose_name = '学分要求'
        verbose_name_plural = '学分要求'
        db_table = 'credit_requirements'
        unique_together = ['template', 'category']

    def __str__(self):
        return f"{self.template} - {self.category}: {self.required_credits}学分"

class TemplateCourse(models.Model):
    """模板课程"""
    template = models.ForeignKey(CurriculumTemplate, on_delete=models.CASCADE, verbose_name='培养方案模板')
    course = models.ForeignKey(Course, on_delete=models.CASCADE, verbose_name='课程')
    category = models.CharField(max_length=50, verbose_name='课程类别')
    recommended_semester = models.IntegerField(verbose_name='推荐学期')
    is_required = models.BooleanField(default=True, verbose_name='是否必修')

    class Meta:
        verbose_name = '模板课程'
        verbose_name_plural = '模板课程'
        db_table = 'template_courses'
        unique_together = ['template', 'course']

    def __str__(self):
        return f"{self.template} - {self.course}"

class UserCurriculumTemplate(models.Model):
    """用户选择的培养方案"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    template = models.ForeignKey(CurriculumTemplate, on_delete=models.CASCADE, verbose_name='培养方案模板')
    is_active = models.BooleanField(default=True, verbose_name='是否当前使用')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        verbose_name = '用户培养方案'
        verbose_name_plural = '用户培养方案'
        db_table = 'user_curriculum_templates'

    def __str__(self):
        return f"{self.user.username} - {self.template}"
