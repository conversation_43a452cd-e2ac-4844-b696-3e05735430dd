from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

router = DefaultRouter()
router.register(r'templates', views.CurriculumTemplateViewSet)
router.register(r'template-courses', views.TemplateCourseViewSet)
router.register(r'credit-requirements', views.CreditRequirementViewSet)
router.register(r'graduation-audit', views.GraduationAuditViewSet, basename='graduation-audit')

urlpatterns = [
    path('', include(router.urls)),
]
