from rest_framework import serializers
from .models import Course, UserCourse

class CourseSerializer(serializers.ModelSerializer):
    class Meta:
        model = Course
        fields = '__all__'

class UserCourseSerializer(serializers.ModelSerializer):
    course = CourseSerializer(read_only=True)
    course_id = serializers.IntegerField(write_only=True, required=False)
    course_manual = CourseSerializer(write_only=True, required=False)
    
    class Meta:
        model = UserCourse
        fields = ['id', 'course', 'course_id', 'course_manual', 'status', 'grade', 'semester_taken', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at']
        
    def validate(self, data):
        # On creation (POST), we need one of them. On update (PATCH), neither is needed.
        if not self.instance and 'course_id' not in data and 'course_manual' not in data:
            raise serializers.ValidationError("Either 'course_id' or 'course_manual' must be provided for creation.")
        if 'course_id' in data and 'course_manual' in data:
            raise serializers.ValidationError("Provide either 'course_id' or 'course_manual', not both.")
        return data

    def create(self, validated_data):
        course_manual_data = validated_data.pop('course_manual', None)
        course_id = validated_data.pop('course_id', None)
        course = None

        if course_id:
            try:
                course = Course.objects.get(pk=course_id)
            except Course.DoesNotExist:
                raise serializers.ValidationError(f"Course with id {course_id} does not exist.")
        elif course_manual_data:
            course, created = Course.objects.get_or_create(
                course_code=course_manual_data['course_code'],
                defaults=course_manual_data
            )
        
        if not course:
             raise serializers.ValidationError("Could not find or create a course.")

        validated_data['user'] = self.context['request'].user
        validated_data['course'] = course
        return super().create(validated_data)
