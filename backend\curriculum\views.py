from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Sum, Q
from .models import CurriculumTemplate, TemplateCourse, CreditRequirement
from courses.models import UserCourse
from .serializers import (
    CurriculumTemplateSerializer, 
    TemplateCourseSerializer, 
    CreditRequirementSerializer
)


class CurriculumTemplateViewSet(viewsets.ModelViewSet):
    queryset = CurriculumTemplate.objects.all()
    serializer_class = CurriculumTemplateSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """根据用户的学校和专业过滤培养方案"""
        user = self.request.user
        queryset = CurriculumTemplate.objects.all()
        
        # 如果用户有学校和专业信息，优先显示匹配的方案
        if user.school and user.major:
            queryset = queryset.filter(school=user.school, major=user.major)
        
        return queryset
    
    @action(detail=True, methods=['get'])
    def template_courses(self, request, pk=None):
        """获取培养方案的课程"""
        template = self.get_object()
        courses = TemplateCourse.objects.filter(template=template)
        serializer = TemplateCourseSerializer(courses, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def credit_requirements(self, request, pk=None):
        """获取培养方案的学分要求"""
        template = self.get_object()
        requirements = CreditRequirement.objects.filter(template=template)
        serializer = CreditRequirementSerializer(requirements, many=True)
        return Response(serializer.data)


class TemplateCourseViewSet(viewsets.ModelViewSet):
    queryset = TemplateCourse.objects.all()
    serializer_class = TemplateCourseSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """根据模板ID过滤课程"""
        queryset = TemplateCourse.objects.all()
        template_id = self.request.query_params.get('template_id')
        if template_id:
            queryset = queryset.filter(template_id=template_id)
        return queryset


class CreditRequirementViewSet(viewsets.ModelViewSet):
    queryset = CreditRequirement.objects.all()
    serializer_class = CreditRequirementSerializer
    permission_classes = [IsAuthenticated]


class GraduationAuditViewSet(viewsets.ViewSet):
    """毕业预审API"""
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def audit(self, request):
        """执行毕业预审"""
        user = request.user
        
        # 获取用户的培养方案
        template = None
        try:
            template = CurriculumTemplate.objects.get(
                school=user.school,
                major=user.major,
                enrollment_year=user.enrollment_year,
                is_active=True
            )
        except CurriculumTemplate.DoesNotExist:
            # 如果没有找到匹配的方案，使用默认方案
            template = CurriculumTemplate.objects.filter(
                school=user.school,
                major=user.major,
                is_active=True
            ).first()
        
        if not template:
            return Response({
                'error': '未找到对应的培养方案'
            }, status=404)
        
        # 获取用户已完成的课程
        user_courses = UserCourse.objects.filter(
            user=user,
            status__in=['completed', 'passed']
        ).select_related('course')
        
        # 获取培养方案的学分要求
        credit_requirements = CreditRequirement.objects.filter(template=template)
        
        # 计算各类别学分统计
        audit_details = []
        total_requirements = 0
        met_requirements = 0
        
        for requirement in credit_requirements:
            category_name = requirement.category
            required_credits = requirement.required_credits
            
            # 计算已完成的学分
            completed_credits = 0
            category_courses = []
            
            for user_course in user_courses:
                if user_course.course.category == category_name:
                    completed_credits += user_course.course.credits
                    category_courses.append({
                        'courseCode': user_course.course.course_code,
                        'courseName': user_course.course.course_name,
                        'credits': user_course.course.credits,
                        'grade': user_course.grade or 'P',
                        'semester': user_course.semester_taken or '未设置'
                    })
            
            # 判断是否满足要求
            is_met = completed_credits >= required_credits
            status = 'passed' if is_met else 'failed'
            
            total_requirements += 1
            if is_met:
                met_requirements += 1
            
            # 生成建议
            suggestions = []
            if not is_met:
                shortage = required_credits - completed_credits
                suggestions.append(f'还需完成{shortage}学分的{category_name}课程')
            
            audit_details.append({
                'name': category_name,
                'status': status,
                'requiredCredits': required_credits,
                'completedCredits': completed_credits,
                'description': requirement.description or f'已完成{completed_credits}学分，需要{required_credits}学分',
                'requirements': [{
                    'id': f'{category_name}-req',
                    'name': f'{category_name}要求',
                    'description': f'需要完成{required_credits}学分的{category_name}课程',
                    'isMet': is_met,
                    'suggestions': suggestions if not is_met else []
                }],
                'courses': category_courses
            })
        
        # 计算总体状态
        unmet_requirements = total_requirements - met_requirements
        overall_status = 'passed' if unmet_requirements == 0 else 'failed'
        
        # 生成摘要
        if overall_status == 'passed':
            summary = '恭喜！您已满足所有毕业要求。'
        else:
            summary = f'您还有{unmet_requirements}项要求未满足，请查看详细信息。'
        
        # 生成建议
        recommendations = []
        if overall_status == 'failed':
            for detail in audit_details:
                if detail['status'] == 'failed':
                    shortage = detail['requiredCredits'] - detail['completedCredits']
                    recommendations.append({
                        'type': 'warning',
                        'title': f'{detail["name"]}学分不足',
                        'content': f'还需完成{shortage}学分的{detail["name"]}课程才能满足毕业要求。'
                    })
        else:
            recommendations.append({
                'type': 'success',
                'title': '毕业要求已满足',
                'content': '您已完成所有毕业要求，可以申请毕业。'
            })
        
        return Response({
            'auditResult': {
                'overallStatus': overall_status,
                'summary': summary,
                'totalRequirements': total_requirements,
                'metRequirements': met_requirements,
                'unmetRequirements': unmet_requirements,
                'auditDate': user.date_joined.strftime('%Y-%m-%d') if user.date_joined else '2024-01-01'
            },
            'auditDetails': audit_details,
            'recommendations': recommendations,
            'template': {
                'id': template.id,
                'school': template.school,
                'major': template.major,
                'totalCreditsRequired': template.total_credits_required
            }
        })
