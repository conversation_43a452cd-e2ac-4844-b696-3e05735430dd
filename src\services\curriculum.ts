import api from '../utils/api'

// 培养方案相关的API接口类型定义
export interface CurriculumTemplate {
  id: number
  school: string
  major: string
  enrollment_year: string
  total_credits_required: number
  description: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface TemplateCourse {
  id: number
  course: number
  course_name: string
  course_code: string
  credits: number
  category: string
  recommended_semester: number
  is_required: boolean
}

export interface CreditRequirement {
  id: number
  template: number
  category: string
  required_credits: number
  description: string
}

// 培养方案API
export const curriculumAPI = {
  // 获取培养方案模板
  getTemplates: (): Promise<CurriculumTemplate[]> => {
    return api.get('/curriculum/templates/')
  },

  // 获取培养方案详情
  getTemplateDetail: (id: number): Promise<CurriculumTemplate> => {
    return api.get(`/curriculum/templates/${id}/`)
  },

  // 获取培养方案的课程
  getTemplateCourses: (templateId: number): Promise<TemplateCourse[]> => {
    return api.get(`/curriculum/templates/${templateId}/template_courses/`)
  },

  // 获取培养方案的学分要求
  getCreditRequirements: (templateId: number): Promise<CreditRequirement[]> => {
    return api.get(`/curriculum/templates/${templateId}/credit_requirements/`)
  },

  // 获取所有模板课程（可按模板ID过滤）
  getAllTemplateCourses: (templateId?: number): Promise<TemplateCourse[]> => {
    const params = templateId ? `?template_id=${templateId}` : ''
    return api.get(`/curriculum/template-courses/${params}`)
  },

  // 获取所有学分要求
  getAllCreditRequirements: (): Promise<CreditRequirement[]> => {
    return api.get('/curriculum/credit-requirements/')
  },

  // 毕业预审API
  getGraduationAudit: (): Promise<any> => {
    return api.get('/curriculum/graduation-audit/audit/')
  }
}

export default curriculumAPI
