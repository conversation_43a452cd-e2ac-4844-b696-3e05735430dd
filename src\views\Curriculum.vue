<template>
  <div class="curriculum">
    <!-- 页面头部 -->
    <div class="curriculum-header">
      <div class="header-content">
        <div class="header-text">
          <h1>培养方案</h1>
          <p>查看你的专业培养方案，了解毕业要求和课程规划</p>
        </div>
        <div class="header-actions" v-if="currentTemplate">
          <el-button type="primary" @click="showTemplateDialog" icon="Switch">
            更换方案
          </el-button>
        </div>
      </div>
    </div>

    <!-- Loading state -->
    <div v-if="isLoading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <!-- 未设置培养方案 -->
    <div v-else-if="!currentTemplate || !currentTemplate.id" class="empty-state">
      <el-empty description="你还没有设置培养方案" :image-size="200">
        <el-button type="primary" size="large" @click="showTemplateDialog">
          <el-icon><Plus /></el-icon>
          现在去选择
        </el-button>
      </el-empty>
    </div>

    <!-- 主要内容 -->
    <div v-else class="curriculum-content">
      <!-- 当前培养方案信息 -->
      <el-card class="current-template-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <div class="header-info">
              <el-icon class="header-icon"><School /></el-icon>
              <span class="header-title">当前培养方案</span>
            </div>
            <el-tag type="success" size="large" v-if="currentTemplate.is_active">
              <el-icon><CircleCheckFilled /></el-icon>
              已激活
            </el-tag>
          </div>
        </template>
        
        <div class="template-info">
          <el-row :gutter="24">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <div class="info-label">
                  <el-icon><OfficeBuilding /></el-icon>
                  学校
                </div>
                <div class="info-value">{{ currentTemplate.school }}</div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <div class="info-label">
                  <el-icon><Reading /></el-icon>
                  专业
                </div>
                <div class="info-value">{{ currentTemplate.major }}</div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <div class="info-label">
                  <el-icon><Calendar /></el-icon>
                  入学年份
                </div>
                <div class="info-value">{{ currentTemplate.enrollment_year }}</div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="24" style="margin-top: 24px;">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <div class="info-label">
                  <el-icon><Medal /></el-icon>
                  总学分要求
                </div>
                <div class="info-value highlight">{{ currentTemplate.total_credits_required }} 学分</div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <div class="info-label">
                  <el-icon><Clock /></el-icon>
                  创建时间
                </div>
                <div class="info-value">{{ formatDate(currentTemplate.created_at) }}</div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <div class="info-label">
                  <el-icon><TrendCharts /></el-icon>
                  总体进度
                </div>
                <div class="info-value">
                  <el-progress 
                    :percentage="overallProgress" 
                    :color="progressColor" 
                    :stroke-width="8"
                    striped
                    striped-flow
                  />
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 学分要求统计 -->
      <el-row :gutter="24" style="margin-top: 24px;">
        <el-col :xs="24" :lg="16">
          <el-card class="requirements-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <div class="header-info">
                  <el-icon class="header-icon"><DataAnalysis /></el-icon>
                  <span class="header-title">学分要求详情</span>
                </div>
                <el-tag :type="overallProgress >= 100 ? 'success' : 'warning'">
                  {{ Math.round(overallProgress) }}% 完成
                </el-tag>
              </div>
            </template>
            
            <div class="requirements-content">
              <div class="requirements-table">
                <div class="table-header">
                  <div class="header-cell">课程类别</div>
                  <div class="header-cell">要求学分</div>
                  <div class="header-cell">已修学分</div>
                  <div class="header-cell">完成进度</div>
                </div>
                <div 
                  v-for="req in creditRequirementsWithProgress" 
                  :key="req.category"
                  class="table-row"
                  :class="{ 'completed': req.progress >= 100 }"
                >
                  <div class="table-cell category-cell">
                    <div class="category-name">{{ req.category }}</div>
                    <div class="category-desc" v-if="req.description">{{ req.description }}</div>
                  </div>
                  <div class="table-cell">
                    <span class="credits-badge required">{{ req.required_credits }}</span>
                  </div>
                  <div class="table-cell">
                    <span class="credits-badge" :class="req.progress >= 100 ? 'completed' : 'current'">
                      {{ req.completedCredits }}
                    </span>
                  </div>
                                     <div class="table-cell progress-cell">
                     <el-progress
                       :percentage="req.progress"
                       :status="req.progress >= 100 ? 'success' : undefined"
                       :stroke-width="8"
                       :show-text="false"
                       striped
                       striped-flow
                       style="flex: 1;"
                     />
                     <span class="progress-text">{{ req.progress }}%</span>
                   </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :lg="8">
          <el-card class="stats-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <div class="header-info">
                  <el-icon class="header-icon"><PieChart /></el-icon>
                  <span class="header-title">学分统计</span>
                </div>
              </div>
            </template>
            
            <div class="stats-content">
              <div class="stats-summary">
                <div class="stat-item">
                  <div class="stat-number completed">{{ totalCompletedCredits }}</div>
                  <div class="stat-label">已完成学分</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number remaining">{{ totalRequiredCredits - totalCompletedCredits }}</div>
                  <div class="stat-label">剩余学分</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number total">{{ totalRequiredCredits }}</div>
                  <div class="stat-label">总计学分</div>
                </div>
              </div>
              
              <div class="stats-progress">
                <el-progress 
                  type="circle" 
                  :percentage="overallProgress"
                  :width="120"
                  :stroke-width="8"
                  :color="progressColor"
                >
                  <template #default="{ percentage }">
                    <div class="progress-content">
                      <span class="progress-number">{{ Math.round(percentage) }}%</span>
                      <span class="progress-label">总进度</span>
                    </div>
                  </template>
                </el-progress>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 课程列表 -->
      <el-card class="course-templates-card" shadow="hover" style="margin-top: 24px;">
        <template #header>
          <div class="card-header">
            <div class="header-info">
              <el-icon class="header-icon"><Document /></el-icon>
              <span class="header-title">推荐课程列表</span>
            </div>
            <div class="header-actions">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索课程..."
                prefix-icon="Search"
                style="width: 300px; margin-right: 12px;"
                clearable
              />
              <el-select v-model="selectedSemester" placeholder="学期筛选" style="width: 120px;" clearable>
                <el-option 
                  v-for="semester in availableSemesters" 
                  :key="semester" 
                  :label="`第${semester}学期`" 
                  :value="semester" 
                />
              </el-select>
            </div>
          </div>
        </template>
        
        <el-tabs v-model="activeTab" type="card" class="course-tabs">
          <el-tab-pane
            v-for="category in courseCategories"
            :key="category.key"
            :name="category.key"
          >
            <template #label>
              <div class="tab-label">
                <span>{{ category.name }}</span>
                <el-badge :value="category.filteredCourses.length" class="tab-badge" />
              </div>
            </template>
            
            <div class="courses-container">
              <el-empty 
                v-if="category.filteredCourses.length === 0" 
                description="没有找到符合条件的课程"
                :image-size="120"
              />
              <div v-else class="template-courses-grid">
                <div
                  v-for="course in category.filteredCourses"
                  :key="course.id"
                  class="template-course-card"
                  :class="{ 
                    'is-completed': isCourseCompleted(course.course.id),
                    'is-in-progress': isCourseInProgress(course.course.id)
                  }"
                >
                  <div class="course-status" v-if="isCourseCompleted(course.course.id)">
                    <el-icon class="status-icon completed"><CircleCheckFilled /></el-icon>
                    <span class="status-text">已完成</span>
                  </div>
                  <div class="course-status in-progress" v-else-if="isCourseInProgress(course.course.id)">
                    <el-icon class="status-icon"><Clock /></el-icon>
                    <span class="status-text">进行中</span>
                  </div>
                  
                  <div class="course-header">
                    <span class="course-code">{{ course.course.course_code }}</span>
                    <el-tag size="small" type="info">{{ course.course.credits }} 学分</el-tag>
                  </div>
                  
                  <h4 class="course-name" :title="course.course.course_name">
                    {{ course.course.course_name }}
                  </h4>
                  
                  <div class="course-meta">
                    <div class="meta-item">
                      <el-icon><Calendar /></el-icon>
                      <span>建议第{{ course.recommended_semester }}学期</span>
                    </div>
                    <div class="meta-item" v-if="course.course.prerequisites">
                      <el-icon><Link /></el-icon>
                      <span>有前置课程</span>
                    </div>
                  </div>
                  
                  <p class="course-description" v-if="course.course.description">
                    {{ course.course.description }}
                  </p>
                  
                  <div class="course-actions">
                    <el-button 
                      v-if="!isCourseCompleted(course.course.id) && !isCourseInProgress(course.course.id)"
                      type="primary" 
                      size="small" 
                      plain
                      @click="addCourseToJourney(course.course)"
                    >
                      <el-icon><Plus /></el-icon>
                      添加到学习计划
                    </el-button>
                    <el-button 
                      size="small" 
                      @click="viewCourseDetails(course.course)"
                    >
                      查看详情
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>

    <!-- 选择培养方案模板对话框 -->
    <el-dialog
      v-model="templateDialogVisible"
      title="选择培养方案模板"
      width="900px"
      :close-on-click-modal="false"
    >
      <div class="template-selection">
        <el-alert
          title="提示"
          description="请选择与你的专业和入学年份相匹配的培养方案模板"
          type="info"
          show-icon
          :closable="false"
          style="margin-bottom: 20px;"
        />
        
        <el-table
          :data="availableTemplates"
          style="width: 100%"
          @row-click="handleTemplateSelection"
          highlight-current-row
          height="400"
        >
          <el-table-column prop="school" label="学校" width="200" />
          <el-table-column prop="major" label="专业" width="180" />
          <el-table-column prop="enrollment_year" label="适用年份" width="120" />
          <el-table-column prop="total_credits_required" label="总学分" width="100">
            <template #default="scope">
              <el-tag>{{ scope.row.total_credits_required }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="scope">
              <el-tag 
                :type="scope.row.id === currentTemplate?.id ? 'success' : 'info'"
              >
                {{ scope.row.id === currentTemplate?.id ? '当前' : '可选' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                @click.stop="handleTemplateSelection(scope.row)"
                :disabled="scope.row.id === currentTemplate?.id"
              >
                {{ scope.row.id === currentTemplate?.id ? '当前方案' : '选择' }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  SuccessFilled, 
  CircleCheckFilled, 
  Plus, 
  School, 
  OfficeBuilding, 
  Reading, 
  Calendar, 
  Medal, 
  Clock, 
  TrendCharts,
  DataAnalysis,
  PieChart,
  Document,
  Link,
  Search,
  Switch
} from '@element-plus/icons-vue'
import { curriculumAPI, type CurriculumTemplate, type CreditRequirement, type TemplateCourse } from '../services/curriculum'
import { useUserStore } from '../stores/user'
import { useCourseStore } from '../stores/course'

const userStore = useUserStore()
const courseStore = useCourseStore()

const isLoading = ref(true)
const templateDialogVisible = ref(false)
const activeTab = ref('')
const searchKeyword = ref('')
const selectedSemester = ref<number | null>(null)

const allTemplates = ref<CurriculumTemplate[]>([])
const currentTemplate = ref<CurriculumTemplate | null>(null)
const creditRequirements = ref<CreditRequirement[]>([])
const templateCourses = ref<TemplateCourse[]>([])
const completedCourseIds = ref<Set<number>>(new Set())
const inProgressCourseIds = ref<Set<number>>(new Set())

// 获取所有可用的培养方案，并尝试找到用户的方案
const availableTemplates = computed(() => {
  return allTemplates.value.filter(t => t.school === userStore.user?.school)
})

// 根据用户的专业和入学年份自动选择一个最匹配的方案
const findBestMatchTemplate = () => {
  if (!userStore.user) return null
  return allTemplates.value.find(t => 
    t.school === userStore.user?.school &&
    t.major === userStore.user?.major &&
    t.enrollment_year === String(userStore.user?.enrollment_year)
  ) || null
}

// 可用学期列表
const availableSemesters = computed(() => {
  const semesters = new Set<number>()
  templateCourses.value.forEach(course => {
    semesters.add(course.recommended_semester)
  })
  return Array.from(semesters).sort((a, b) => a - b)
})

// 总学分统计
const totalRequiredCredits = computed(() => {
  return creditRequirements.value.reduce((sum, req) => sum + req.required_credits, 0)
})

const totalCompletedCredits = computed(() => {
  return creditRequirementsWithProgress.value.reduce((sum, req) => sum + req.completedCredits, 0)
})

const overallProgress = computed(() => {
  if (totalRequiredCredits.value === 0) return 0
  return Math.min((totalCompletedCredits.value / totalRequiredCredits.value) * 100, 100)
})

const progressColor = computed(() => {
  const progress = overallProgress.value
  if (progress >= 100) return '#67c23a'
  if (progress >= 80) return '#409eff'
  if (progress >= 60) return '#e6a23c'
  return '#f56c6c'
})

const showTemplateDialog = () => {
  templateDialogVisible.value = true
}

const fetchAllData = async (templateId: number) => {
  isLoading.value = true
  try {
    const [requirements, courses] = await Promise.all([
      curriculumAPI.getCreditRequirements(templateId),
      curriculumAPI.getTemplateCourses(templateId)
    ])
    creditRequirements.value = requirements
    templateCourses.value = courses

    // 假设第一个标签页为默认
    if (courseCategories.value.length > 0) {
      activeTab.value = courseCategories.value[0].key
    }

  } catch (error) {
    console.error("Failed to fetch curriculum details:", error)
    ElMessage.error("获取培养方案详情失败")
  } finally {
    isLoading.value = false
  }
}

const creditRequirementsWithProgress = computed(() => {
  const completedCreditsByCategory: { [key: string]: number } = {}

  courseStore.userCourses.forEach(userCourse => {
    if (userCourse.status === 'completed') {
      const category = userCourse.course.category
      completedCreditsByCategory[category] = (completedCreditsByCategory[category] || 0) + userCourse.course.credits
    }
  })

  return creditRequirements.value.map(req => {
    const completed = completedCreditsByCategory[req.category] || 0
    const progress = req.required_credits > 0 ? Math.round((completed / req.required_credits) * 100) : 100
    return {
      ...req,
      completedCredits: completed,
      progress: Math.min(progress, 100)
    }
  })
})

const courseCategories = computed(() => {
  const categories: { [key: string]: { key: string; name: string; courses: TemplateCourse[]; filteredCourses: TemplateCourse[] } } = {}
  
  templateCourses.value.forEach(course => {
    const categoryName = course.category
    if (!categories[categoryName]) {
      categories[categoryName] = {
        key: categoryName,
        name: categoryName,
        courses: [],
        filteredCourses: []
      }
    }
    categories[categoryName].courses.push(course)
  })
  
  // 应用搜索和筛选
  Object.values(categories).forEach(category => {
    category.filteredCourses = category.courses.filter(course => {
      const matchesSearch = !searchKeyword.value || 
        course.course.course_name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
        course.course.course_code.toLowerCase().includes(searchKeyword.value.toLowerCase())
      
      const matchesSemester = selectedSemester.value === null || 
        course.recommended_semester === selectedSemester.value
      
      return matchesSearch && matchesSemester
    })
  })
  
  return Object.values(categories)
})

const isCourseCompleted = (courseId: number) => {
  return completedCourseIds.value.has(courseId)
}

const isCourseInProgress = (courseId: number) => {
  return inProgressCourseIds.value.has(courseId)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const addCourseToJourney = (course: any) => {
  // 这里应该调用API添加课程到学习计划
  ElMessage.success(`已将 "${course.course_name}" 添加到学习计划`)
}

const viewCourseDetails = (course: any) => {
  // 这里应该打开课程详情对话框或跳转到详情页
  ElMessage.info(`查看课程详情: ${course.course_name}`)
}

const handleTemplateSelection = async (template: CurriculumTemplate) => {
  ElMessageBox.confirm(
    `确定要切换到"${template.school} - ${template.major}"的培养方案吗？`,
    '确认切换',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    currentTemplate.value = template
    await fetchAllData(template.id)
    templateDialogVisible.value = false
    ElMessage.success('培养方案切换成功')
  })
}

onMounted(async () => {
  try {
    await Promise.all([
      courseStore.fetchUserCourses(),
      userStore.refreshProfile()
    ])
    
    // 更新已完成和进行中课程的ID集合
    completedCourseIds.value = new Set(
      courseStore.userCourses
        .filter(c => c.status === 'completed')
        .map(c => c.course.id)
    )
    
    inProgressCourseIds.value = new Set(
      courseStore.userCourses
        .filter(c => c.status === 'in_progress')
        .map(c => c.course.id)
    )

    allTemplates.value = await curriculumAPI.getTemplates()
    
    // 尝试找到并设置用户的当前方案
    const bestMatch = findBestMatchTemplate()
    if (bestMatch) {
      currentTemplate.value = bestMatch
      await fetchAllData(bestMatch.id)
    } else if (availableTemplates.value.length > 0) {
      // 如果没有完美匹配，但有同校方案，默认使用第一个
      currentTemplate.value = availableTemplates.value[0]
      await fetchAllData(availableTemplates.value[0].id)
    }

  } catch (error) {
    console.error("Failed to initialize curriculum page:", error)
    ElMessage.error("页面加载失败")
  } finally {
    isLoading.value = false
  }
})
</script>

<style scoped>
.curriculum {
  padding: 24px;
  background-color: #f8fafc;
  height: 100%;
  overflow-y: auto;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.curriculum-content {
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  flex: 1;
  min-height: 0;
}

.curriculum-header {
  margin-bottom: 24px;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px;
  border-radius: 16px;
  color: white;
  position: relative;
  overflow: hidden;
}

.header-content::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -10%;
  width: 200px;
  height: 200px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.header-text h1 {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.header-text p {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

.loading-container, .empty-state {
  padding: 40px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

/* 卡片样式 */
.current-template-card,
.requirements-card,
.stats-card,
.course-templates-card {
  border: none;
  border-radius: 16px;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  font-size: 20px;
  color: #667eea;
}

.header-title {
  font-size: 18px;
  color: #1a202c;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 模板信息样式 */
.template-info {
  padding: 8px 0;
}

.info-item {
  margin-bottom: 16px;
}

.info-label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #64748b;
  font-size: 14px;
  margin-bottom: 6px;
}

.info-value {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
}

.info-value.highlight {
  color: #667eea;
  font-size: 18px;
}

/* 统计卡片样式 */
.stats-content {
  padding: 20px 0;
}

.stats-summary {
  display: flex;
  justify-content: space-around;
  margin-bottom: 24px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-number.completed {
  color: #67c23a;
}

.stat-number.remaining {
  color: #e6a23c;
}

.stat-number.total {
  color: #409eff;
}

.stat-label {
  font-size: 12px;
  color: #64748b;
}

.stats-progress {
  display: flex;
  justify-content: center;
}

.progress-content {
  text-align: center;
}

.progress-number {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
}

.progress-label {
  font-size: 12px;
  color: #64748b;
}

/* 学分要求表格样式 */
.requirements-content {
  padding: 8px 0;
}

.requirements-table {
  background: #f8fafc;
  border-radius: 12px;
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 2fr;
  gap: 16px;
  padding: 16px 20px;
  background: #e2e8f0;
  font-weight: 600;
  color: #475569;
  font-size: 14px;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 2fr;
  gap: 16px;
  padding: 20px;
  border-bottom: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.table-row:hover {
  background: #f1f5f9;
}

.table-row.completed {
  background: #f0fdf4;
  border-color: #bbf7d0;
}

.table-cell {
  display: flex;
  align-items: center;
}

.category-cell {
  flex-direction: column;
  align-items: flex-start;
}

.category-name {
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 4px;
}

.category-desc {
  font-size: 12px;
  color: #64748b;
}

.credits-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
}

.credits-badge.required {
  background: #e0e7ff;
  color: #3730a3;
}

.credits-badge.completed {
  background: #dcfce7;
  color: #166534;
}

.credits-badge.current {
  background: #fef3c7;
  color: #92400e;
}

 .progress-cell {
   gap: 12px;
   align-items: center;
 }
 
 .progress-text {
   font-size: 14px;
   font-weight: 600;
   color: #64748b;
   min-width: 45px;
   text-align: right;
   white-space: nowrap;
 }

/* 课程标签页样式 */
.course-tabs {
  margin-top: 20px;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab-badge {
  transform: scale(0.8);
}

.courses-container {
  margin-top: 20px;
}

/* 课程网格样式 */
.template-courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 24px;
  margin-top: 20px;
}

.template-course-card {
  position: relative;
  padding: 24px;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  background: white;
  transition: all 0.3s ease;
  cursor: pointer;
}

.template-course-card:hover {
  border-color: #667eea;
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(102, 126, 234, 0.15);
}

.template-course-card.is-completed {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border-color: #22c55e;
}

.template-course-card.is-in-progress {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-color: #f59e0b;
}

.course-status {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.course-status.in-progress {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.status-icon {
  font-size: 14px;
}

.course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.course-code {
  background: #667eea;
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.course-name {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 16px 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.course-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #64748b;
  font-size: 14px;
}

.course-description {
  color: #64748b;
  font-size: 14px;
  line-height: 1.6;
  margin: 0 0 20px 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.course-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

 /* 响应式设计 */
 @media (max-width: 768px) {
   .curriculum {
     padding: 16px;
   }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
    padding: 20px;
  }
  
  .header-text h1 {
    font-size: 24px;
  }
  
  .template-courses-grid {
    grid-template-columns: 1fr;
  }
  
     .table-header,
   .table-row {
     grid-template-columns: 1fr;
     gap: 12px;
   }
   
   .table-row {
     padding: 16px;
   }
   
   .progress-cell {
     flex-direction: column;
     align-items: flex-start;
     gap: 8px;
   }
   
   .progress-text {
     text-align: left;
     min-width: auto;
   }
  
  .header-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .header-actions .el-input,
  .header-actions .el-select {
    width: 100% !important;
  }
}

@media (max-width: 1200px) {
  .template-courses-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
}

/* 对话框样式 */
.template-selection {
  padding: 0;
}

:deep(.el-dialog__header) {
  padding: 24px 24px 0;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-card__header) {
  padding: 24px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-card__body) {
  padding: 24px;
}

:deep(.el-tabs__header) {
  margin: 0 0 20px;
}

:deep(.el-tabs__item) {
  padding: 12px 20px;
  font-weight: 500;
}

:deep(.el-progress-bar__outer) {
  border-radius: 10px;
}

 :deep(.el-progress-bar__inner) {
   border-radius: 10px;
 }

 /* 防止页面出现额外滚动空间 */
 :deep(body) {
   overflow-x: hidden;
 }
 </style>
