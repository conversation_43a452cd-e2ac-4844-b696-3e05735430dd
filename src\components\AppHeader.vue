<template>
  <div class="app-header">
    <div class="header-left">
      <div class="brand">
        <div class="logo">
          <el-icon size="28"><School /></el-icon>
        </div>
        <div class="brand-text">
          <h1>学途管家</h1>
          <span class="subtitle">Academic Journey Steward</span>
        </div>
      </div>
    </div>
    
    <div class="header-center">
      <div class="breadcrumb">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>{{ currentPageTitle }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </div>

    <div class="header-right">
      <div class="header-actions">
        <!-- 通知按钮 -->
        <el-button 
          type="text" 
          class="action-btn"
          @click="showNotifications"
        >
          <el-badge :value="notificationCount" class="notification-badge">
            <el-icon size="18"><Bell /></el-icon>
          </el-badge>
        </el-button>
        
        <!-- 帮助按钮 -->
        <el-button 
          type="text" 
          class="action-btn"
          @click="showHelp"
        >
          <el-icon size="18"><QuestionFilled /></el-icon>
        </el-button>
      </div>

      <!-- 用户下拉菜单 -->
      <el-dropdown 
        class="user-dropdown"
        @command="handleCommand"
      >
        <div class="user-info">
          <el-avatar 
            :size="36" 
            :src="userStore.user?.avatar || undefined"
            class="user-avatar"
          >
            <el-icon><User /></el-icon>
          </el-avatar>
          <div class="user-details">
            <span class="username">{{ displayName }}</span>
            <span class="user-role">{{ userStore.user?.major || '学生' }}</span>
          </div>
          <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">
              <el-icon><User /></el-icon>
              个人信息
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <el-icon><Setting /></el-icon>
              设置
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  ArrowDown, 
  User, 
  Setting, 
  SwitchButton, 
  School,
  Bell,
  QuestionFilled
} from '@element-plus/icons-vue'
import { useUserStore } from '../stores/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const displayName = computed(() => {
  return userStore.user?.real_name || userStore.user?.username || '未登录'
})

const currentPageTitle = computed(() => {
  const titleMap: { [key: string]: string } = {
    '/dashboard': '进度仪表盘',
    '/journey': '我的学途',
    '/curriculum': '培养方案管理',
    '/audit': '毕业预审',
    '/profile': '个人信息'
  }
  return titleMap[route.path] || '学途管家'
})

const notificationCount = computed(() => {
  // 这里可以根据实际需求计算通知数量
  return 3
})

const handleCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      ElMessage.info('设置功能开发中...')
      break
    case 'logout':
      userStore.logout()
      router.push('/login')
      break
  }
}

const showNotifications = () => {
  ElMessage.info('通知功能开发中...')
}

const showHelp = () => {
  ElMessage.info('帮助功能开发中...')
}
</script>

<style scoped>
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  height: 64px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1000;
}

.header-left {
  display: flex;
  align-items: center;
  min-width: 250px;
}

.brand {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.brand-text h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.subtitle {
  font-size: 11px;
  opacity: 0.8;
  font-weight: 400;
  margin-top: 2px;
  display: block;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.breadcrumb {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  font-weight: 500;
}

.breadcrumb :deep(.el-breadcrumb__inner) {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.breadcrumb :deep(.el-breadcrumb__separator) {
  color: rgba(255, 255, 255, 0.6);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-btn {
  color: rgba(255, 255, 255, 0.9) !important;
  padding: 8px !important;
  border-radius: 6px !important;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: white !important;
}

.notification-badge :deep(.el-badge__content) {
  background: #f56c6c;
  border: 2px solid white;
}

.user-dropdown {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 6px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.user-info:hover {
  background: rgba(255, 255, 255, 0.1);
}

.user-avatar {
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.username {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.2;
}

.user-role {
  font-size: 12px;
  opacity: 0.8;
  line-height: 1.2;
}

.dropdown-icon {
  font-size: 12px;
  opacity: 0.8;
  transition: transform 0.3s ease;
}

.user-dropdown:hover .dropdown-icon {
  transform: rotate(180deg);
}

/* 下拉菜单样式 */
:deep(.el-dropdown-menu) {
  margin-top: 8px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-dropdown-menu__item) {
  padding: 12px 16px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.el-dropdown-menu__item:hover) {
  background: #f5f7fa;
}

:deep(.el-dropdown-menu__item--divided) {
  border-top: 1px solid #ebeef5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-header {
    padding: 0 16px;
  }
  
  .header-center {
    display: none;
  }
  
  .brand-text h1 {
    font-size: 18px;
  }
  
  .subtitle {
    display: none;
  }
  
  .user-details {
    display: none;
  }
}
</style>
