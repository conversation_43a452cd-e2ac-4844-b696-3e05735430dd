<template>
  <div class="journey">
    <div class="journey-header">
      <h1>我的学途</h1>
      <p>规划你的学业路径，追踪课程进度</p>
    </div>

    <div class="journey-controls">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-select v-model="selectedSemester" placeholder="选择学期" style="width: 100%">
            <el-option
              v-for="semester in semesters"
              :key="semester.value"
              :label="semester.label"
              :value="semester.value"
            />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-select v-model="selectedStatus" placeholder="筛选状态" style="width: 100%">
            <el-option label="全部" value="" />
            <el-option label="计划中" value="planned" />
            <el-option label="进行中" value="in-progress" />
            <el-option label="已完成" value="completed" />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="showAddCourseDialog">
            <el-icon><Plus /></el-icon>
            添加课程
          </el-button>
        </el-col>
      </el-row>
    </div>

    <div class="semester-container">
      <div
        v-for="semester in filteredSemesters"
        :key="semester.id"
        class="semester-section"
      >
        <div class="semester-header">
          <h3>{{ semester.name }}</h3>
          <div class="semester-stats">
            <span>{{ semester.totalCredits }} 学分</span>
            <span>{{ semester.courses.length }} 门课程</span>
          </div>
        </div>
        
        <div class="courses-grid">
          <div
            v-for="course in semester.courses"
            :key="course.id"
            class="course-card"
            :class="[`status-${course.status}`]"
          >
            <div class="course-actions">
               <el-button-group>
                <el-button size="small" @click="editCourse(course)">编辑</el-button>
                <el-button type="danger" size="small" @click="deleteCourse(course.id)">删除</el-button>
              </el-button-group>
            </div>
            <div class="course-header" @click="editCourse(course)">
              <span class="course-code">{{ course.courseCode }}</span>
              <el-tag
                :type="getStatusType(course.status)"
                size="small"
              >
                {{ getStatusText(course.status) }}
              </el-tag>
            </div>
            <h4 class="course-name">{{ course.courseName }}</h4>
            <div class="course-info">
              <span class="credits">{{ course.credits }} 学分</span>
              <span v-if="course.grade" class="grade">{{ course.grade }}</span>
            </div>
            <div class="course-category">{{ course.category }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加/编辑课程对话框 -->
    <el-dialog
      v-model="courseDialogVisible"
      :title="isEditMode ? '编辑课程' : '添加课程'"
      width="600px"
    >
      <el-form
        ref="courseFormRef"
        :model="courseForm"
        :rules="courseRules"
        label-width="120px"
      >
        <el-form-item label="添加方式" v-if="!isEditMode">
          <el-switch
            v-model="addFromCatalog"
            active-text="从课程库选择"
            inactive-text="手动输入新课程"
          />
        </el-form-item>

        <template v-if="addFromCatalog">
          <el-form-item label="选择课程" prop="course_id">
              <el-select 
                v-model="courseForm.course_id" 
                placeholder="搜索或选择一门课程"
                filterable
                style="width: 100%"
                :disabled="isEditMode"
              >
                <el-option
                  v-for="course in allCourses"
                  :key="course.id"
                  :label="`${course.course_code} - ${course.course_name}`"
                  :value="course.id"
                />
              </el-select>
          </el-form-item>
        </template>
        
        <template v-else-if="!isEditMode">
          <el-form-item label="课程代码" prop="course_code">
            <el-input v-model="courseForm.course_code" placeholder="课程的唯一代码, 如 CS101" />
          </el-form-item>
          <el-form-item label="课程名称" prop="course_name">
            <el-input v-model="courseForm.course_name" placeholder="课程的完整名称" />
          </el-form-item>
          <el-form-item label="学分" prop="credits">
            <el-input-number v-model="courseForm.credits" :min="0.5" :step="0.5" />
          </el-form-item>
          <el-form-item label="课程类别" prop="category">
            <el-select v-model="courseForm.category" placeholder="选择课程类别" style="width: 100%">
              <el-option label="通识必修" value="通识必修" />
              <el-option label="通识选修" value="通识选修" />
              <el-option label="学科基础" value="学科基础" />
              <el-option label="专业必修" value="专业必修" />
              <el-option label="专业选修" value="专业选修" />
              <el-option label="实践教学" value="实践教学" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-form-item>
        </template>

        <el-form-item label="学期" prop="semester_taken">
          <el-select v-model="courseForm.semester_taken" placeholder="选择修读或计划修读的学期" style="width: 100%">
            <el-option
              v-for="semester in semesters"
              :key="semester.value"
              :label="semester.label"
              :value="semester.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="courseForm.status" style="width: 100%">
            <el-option label="计划中" value="planned" />
            <el-option label="进行中" value="in-progress" />
            <el-option label="已完成" value="completed" />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="courseForm.status === 'completed'"
          label="成绩"
          prop="grade"
        >
          <el-input v-model="courseForm.grade" placeholder="例如: A+, B, 88, 3.7" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="courseDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveCourse">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { courseAPI, type UserCourse, type Course as AllCourses, type NewUserCourseData } from '../services/course'

interface UserCourseDisplay {
  id: number
  courseId: number
  courseCode: string
  courseName: string
  credits: number
  category: string
  semester: string
  status: 'planned' | 'in-progress' | 'completed'
  grade?: string | null
}

interface Semester {
  id: string
  name: string
  courses: UserCourseDisplay[]
  totalCredits: number
}

const selectedSemester = ref('')
const selectedStatus = ref('')
const courseDialogVisible = ref(false)
const isEditMode = ref(false)
const courseFormRef = ref<FormInstance>()
const currentEditCourseId = ref<number | null>(null)
const isLoading = ref(true)
const addFromCatalog = ref(true)

const allCourses = ref<AllCourses[]>([])

const semesters = ref([
  { label: '2021-2022学年第1学期', value: '2021-1' },
  { label: '2021-2022学年第2学期', value: '2021-2' },
  { label: '2022-2023学年第1学期', value: '2022-1' },
  { label: '2022-2023学年第2学期', value: '2022-2' },
  { label: '2023-2024学年第1学期', value: '2023-1' },
  { label: '2023-2024学年第2学期', value: '2023-2' },
  { label: '2024-2025学年第1学期', value: '2024-1' },
  { label: '2024-2025学年第2学期', value: '2024-2' }
])

const courseForm = reactive({
  id: null as number | null,
  course_id: null as number | null,
  course_code: '',
  course_name: '',
  credits: 3,
  category: '专业选修',
  semester_taken: '',
  status: 'planned' as UserCourse['status'],
  grade: ''
})

const courseRules = computed(() => {
  const rules: any = {
    semester_taken: [
      { required: true, message: '请选择学期', trigger: 'change' }
    ],
    status: [
      { required: true, message: '请选择状态', trigger: 'change' }
    ]
  }

  if (addFromCatalog.value) {
    rules.course_id = [
      { required: true, message: '请选择课程', trigger: 'change' }
    ]
  } else {
    rules.course_code = [
      { required: true, message: '请输入课程代码', trigger: 'blur' }
    ]
    rules.course_name = [
      { required: true, message: '请输入课程名称', trigger: 'blur' }
    ]
    rules.credits = [
      { required: true, message: '请输入学分', trigger: 'blur' },
      { type: 'number', min: 0.5, message: '学分必须大于0', trigger: 'blur' }
    ]
    rules.category = [
      { required: true, message: '请选择课程类别', trigger: 'change' }
    ]
  }

  return rules
})

const userCourses = ref<UserCourse[]>([])

const groupedSemesters = computed(() => {
  const grouped: { [key: string]: Semester } = {}

  userCourses.value.forEach(uc => {
    const semesterId = uc.semester_taken
    if (!grouped[semesterId]) {
      const semesterInfo = semesters.value.find(s => s.value === semesterId)
      grouped[semesterId] = {
        id: semesterId,
        name: semesterInfo ? semesterInfo.label : `未知学期 (${semesterId})`,
        courses: [],
        totalCredits: 0
      }
    }
    grouped[semesterId].courses.push({
      id: uc.id,
      courseId: uc.course.id,
      courseCode: uc.course.course_code,
      courseName: uc.course.course_name,
      credits: uc.course.credits,
      category: uc.course.category,
      semester: uc.semester_taken,
      status: uc.status,
      grade: uc.grade || undefined
    })
    grouped[semesterId].totalCredits += uc.course.credits
  })

  return Object.values(grouped).sort((a, b) => b.id.localeCompare(a.id));
})

const filteredSemesters = computed(() => {
  let filtered = groupedSemesters.value

  if (selectedSemester.value) {
    filtered = filtered.filter(s => s.id === selectedSemester.value)
  }

  if (selectedStatus.value) {
    filtered = filtered.map(semester => ({
      ...semester,
      courses: semester.courses.filter(course => course.status === selectedStatus.value)
    })).filter(semester => semester.courses.length > 0)
  }

  return filtered
})

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    'planned': 'info',
    'in-progress': 'warning',
    'completed': 'success'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    'planned': '计划中',
    'in-progress': '进行中',
    'completed': '已完成'
  }
  return texts[status] || '未知'
}

const fetchAllCourses = async () => {
  try {
    allCourses.value = await courseAPI.getAllCourses();
  } catch (error) {
    ElMessage.error('获取所有课程列表失败')
  }
}

const fetchUserCourses = async () => {
  isLoading.value = true
  try {
    const data = await courseAPI.getUserCourses()
    userCourses.value = data
  } catch (error) {
    console.error('Failed to fetch user courses:', error)
    ElMessage.error('获取我的课程失败')
  } finally {
    isLoading.value = false
  }
}

const showAddCourseDialog = () => {
  isEditMode.value = false
  resetCourseForm()
  courseDialogVisible.value = true
}

const editCourse = (course: UserCourseDisplay) => {
  isEditMode.value = true
  currentEditCourseId.value = course.id
  
  courseForm.id = course.id
  courseForm.course_id = course.courseId
  courseForm.semester_taken = course.semester
  courseForm.status = course.status
  courseForm.grade = course.grade || ''

  courseDialogVisible.value = true
}

const resetCourseForm = () => {
  courseForm.id = null
  courseForm.course_id = null
  courseForm.course_code = ''
  courseForm.course_name = ''
  courseForm.credits = 3
  courseForm.category = '专业选修'
  courseForm.semester_taken = ''
  courseForm.status = 'planned'
  courseForm.grade = ''
  currentEditCourseId.value = null
}

const saveCourse = async () => {
  if (!courseFormRef.value) return
  
  await courseFormRef.value.validate(async (valid) => {
    if (valid) {
      let data: NewUserCourseData | { course_manual: any; [key: string]: any }

      if (!isEditMode.value && !addFromCatalog.value) {
        // Manual Add Mode
        data = {
          course_manual: {
            course_code: courseForm.course_code,
            course_name: courseForm.course_name,
            credits: courseForm.credits,
            category: courseForm.category,
            description: '' // 可以为空
          },
          status: courseForm.status,
          semester_taken: courseForm.semester_taken,
          grade: courseForm.grade || null
        }
      } else {
        // Catalog Add or Edit Mode
        if (!courseForm.course_id) {
          ElMessage.error('请选择一门课程')
          return
        }
        data = {
          course_id: courseForm.course_id as number,
          status: courseForm.status,
          semester_taken: courseForm.semester_taken,
          grade: courseForm.grade || null
        }
      }
      
      try {
        if (isEditMode.value && courseForm.id) {
          // 在编辑模式下，我们只发送可以更新的字段
          const updateData = {
            status: courseForm.status,
            semester_taken: courseForm.semester_taken,
            grade: courseForm.grade || null
          }
          await courseAPI.updateUserCourse(courseForm.id, updateData)
          ElMessage.success('课程更新成功')
        } else {
          // 添加模式
          await courseAPI.addUserCourse(data)
          ElMessage.success('课程添加成功')
        }
        await fetchUserCourses() // Refresh data
        courseDialogVisible.value = false
      } catch (error: any) {
        console.error('Failed to save course:', error)
        const errorMessage = error.response?.data?.detail || '保存课程失败'
        ElMessage.error(errorMessage)
      }
    }
  })
}

const deleteCourse = async (courseId: number) => {
  ElMessageBox.confirm(
    '确定要从您的学途中删除这门课程吗？此操作无法撤销。',
    '警告',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      await courseAPI.deleteUserCourse(courseId);
      ElMessage.success('课程已删除');
      await fetchUserCourses(); // Refresh data
    } catch (error) {
      console.error('Failed to delete course:', error)
      ElMessage.error('删除课程失败');
    }
  }).catch(() => {
    // User cancelled
  });
};

onMounted(() => {
  fetchUserCourses()
  fetchAllCourses()
})

watch(isEditMode, (isEditing) => {
  if (isEditing) {
    addFromCatalog.value = true;
  }
});
</script>

<style scoped>
.journey {
  padding: 20px;
}

.journey-header {
  margin-bottom: 30px;
}

.journey-header h1 {
  color: #303133;
  margin-bottom: 8px;
}

.journey-header p {
  color: #606266;
  font-size: 16px;
  margin: 0;
}

.journey-controls {
  margin-bottom: 30px;
}

.semester-section {
  margin-bottom: 40px;
}

.semester-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
  margin-bottom: 20px;
}

.semester-header h3 {
  margin: 0;
  color: #303133;
}

.semester-stats span {
  margin-left: 20px;
  color: #909399;
  font-size: 14px;
}

.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.course-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s;
  background: white;
  position: relative;
}

.course-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.course-card:hover .course-actions {
  opacity: 1;
}

.course-actions {
  position: absolute;
  top: 15px;
  right: 15px;
  opacity: 0;
  transition: opacity 0.2s;
}

.course-header, .course-name, .course-info, .course-category {
  cursor: pointer;
}

.course-card.status-planned {
  border-left: 4px solid #909399;
}

.course-card.status-in-progress {
  border-left: 4px solid #e6a23c;
}

.course-card.status-completed {
  border-left: 4px solid #67c23a;
}

.course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.course-code {
  font-weight: bold;
  color: #409eff;
}

.course-name {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.course-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.credits {
  color: #606266;
  font-size: 14px;
}

.grade {
  font-weight: bold;
  color: #67c23a;
}

.course-category {
  font-size: 12px;
  color: #909399;
  background-color: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
}
</style>
