from django.core.management.base import BaseCommand
from courses.models import Course
from curriculum.models import CurriculumTemplate, CreditRequirement, TemplateCourse
from django.contrib.auth import get_user_model

User = get_user_model()

class Command(BaseCommand):
    help = '创建示例数据用于开发和测试'

    def handle(self, *args, **options):
        self.stdout.write('开始创建示例数据...')
        
        # 创建示例课程
        courses_data = [
            {'course_code': 'CS101', 'course_name': '程序设计基础', 'credits': 4, 'description': '计算机编程入门课程'},
            {'course_code': 'MATH101', 'course_name': '高等数学I', 'credits': 5, 'description': '微积分基础'},
            {'course_code': 'ENG101', 'course_name': '大学英语I', 'credits': 4, 'description': '英语基础课程'},
            {'course_code': 'PE101', 'course_name': '体育I', 'credits': 1, 'description': '体育基础课程'},
            {'course_code': 'POL101', 'course_name': '思想道德修养与法律基础', 'credits': 3, 'description': '思政必修课'},
            {'course_code': 'CS201', 'course_name': '数据结构与算法', 'credits': 4, 'description': '计算机专业核心课程'},
            {'course_code': 'CS202', 'course_name': '计算机组成原理', 'credits': 3, 'description': '计算机硬件基础'},
            {'course_code': 'CS301', 'course_name': '数据库系统原理', 'credits': 3, 'description': '数据库设计与应用'},
            {'course_code': 'CS302', 'course_name': '软件工程', 'credits': 3, 'description': '软件开发方法论'},
            {'course_code': 'CS401', 'course_name': '毕业设计', 'credits': 6, 'description': '毕业设计项目'},
        ]
        
        for course_data in courses_data:
            course, created = Course.objects.get_or_create(
                course_code=course_data['course_code'],
                defaults=course_data
            )
            if created:
                self.stdout.write(f'创建课程: {course.course_name}')
        
        # 创建培养方案模板
        template, created = CurriculumTemplate.objects.get_or_create(
            school='某某大学',
            major='计算机科学与技术',
            enrollment_year='2021',
            defaults={
                'total_credits_required': 160,
                'description': '计算机科学与技术专业2021级培养方案'
            }
        )
        
        if created:
            self.stdout.write(f'创建培养方案: {template}')
            
            # 创建学分要求
            requirements_data = [
                {'category': '通识必修', 'required_credits': 40, 'description': '思政、英语、体育等必修课程'},
                {'category': '学科基础', 'required_credits': 30, 'description': '数学、物理等基础学科课程'},
                {'category': '专业必修', 'required_credits': 50, 'description': '专业核心课程'},
                {'category': '专业选修', 'required_credits': 25, 'description': '专业方向选修课程'},
                {'category': '实践教学', 'required_credits': 15, 'description': '实习、毕业设计等实践环节'},
            ]
            
            for req_data in requirements_data:
                CreditRequirement.objects.create(
                    template=template,
                    **req_data
                )
                self.stdout.write(f'创建学分要求: {req_data["category"]}')
        
        # 创建测试用户
        test_user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'username': 'student',
                'real_name': '张三',
                'student_id': '2021001001',
                'school': '某某大学',
                'college': '计算机科学与技术学院',
                'major': '计算机科学与技术',
                'enrollment_year': 2021,
            }
        )
        
        if created:
            test_user.set_password('password123')
            test_user.save()
            self.stdout.write(f'创建测试用户: {test_user.email}')
        
        self.stdout.write(self.style.SUCCESS('示例数据创建完成！'))
        self.stdout.write('管理员登录: http://localhost:8000/admin/')
        self.stdout.write('用户名: admin, 邮箱: <EMAIL>')
        self.stdout.write('测试用户: <EMAIL>, 密码: password123')
