from rest_framework import serializers
from .models import CurriculumTemplate, TemplateCourse, CreditRequirement


class CurriculumTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = CurriculumTemplate
        fields = '__all__'


class TemplateCourseSerializer(serializers.ModelSerializer):
    course_name = serializers.CharField(source='course.course_name', read_only=True)
    course_code = serializers.CharField(source='course.course_code', read_only=True)
    credits = serializers.IntegerField(source='course.credits', read_only=True)
    
    class Meta:
        model = TemplateCourse
        fields = ['id', 'course', 'course_name', 'course_code', 'credits', 
                 'category', 'recommended_semester', 'is_required']


class CreditRequirementSerializer(serializers.ModelSerializer):
    class Meta:
        model = CreditRequirement
        fields = '__all__'
